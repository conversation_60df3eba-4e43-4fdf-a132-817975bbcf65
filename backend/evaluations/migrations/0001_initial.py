# Generated by Django 5.2.4 on 2025-07-21 19:03

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Evaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.IntegerField()),
                ('employee_pair_id', models.IntegerField()),
                ('rating', models.IntegerField()),
                ('comment', models.TextField(blank=True)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('token', models.UUIDField()),
                ('used', models.BooleanField(default=False)),
            ],
        ),
    ]
