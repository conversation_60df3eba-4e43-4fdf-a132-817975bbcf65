# Generated by Django 5.2.4 on 2025-07-24 19:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0001_initial'),
        ('evaluations', '0001_initial'),
        ('matching', '0002_alter_campaignmatchingcriteria_rule'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='evaluation',
            name='employee_id',
        ),
        migrations.RemoveField(
            model_name='evaluation',
            name='employee_pair_id',
        ),
        migrations.AddField(
            model_name='evaluation',
            name='employee',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='employees.employee'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='employee_pair',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='matching.employeepair'),
        ),
    ]
