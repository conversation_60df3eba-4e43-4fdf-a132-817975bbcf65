# tests/__init__.py
"""
Test suite for the Coffee Meetings Platform

This directory contains all test files organized by application and functionality.
Tests are separated from the main application logic to maintain clean architecture.

Structure:
- test_campaigns.py: Tests for campaigns app functionality
- test_users.py: Tests for users app functionality  
- test_employees.py: Tests for employees app functionality
- test_evaluations.py: Tests for evaluations app functionality
- test_matching.py: Tests for matching app functionality
- fixtures/: Test data fixtures
- utils/: Test utilities and helpers
"""
