# Generated by Django 5.2.4 on 2025-07-30 01:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0004_rename_hr_manager_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='CampaignWorkflowLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_number', models.IntegerField()),
                ('action', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('user', models.CharField(blank=True, max_length=100)),
                ('data', models.JSONField(default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('campaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='workflow_logs', to='campaigns.campaign')),
            ],
            options={
                'db_table': 'campaign_workflow_log',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='CampaignWorkflowState',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_step', models.IntegerField(choices=[(1, 'Create Campaign'), (2, 'Upload Employees'), (3, 'Define Criteria'), (4, 'Generate Pairs'), (5, 'Confirm and Send')], default=1)),
                ('completed_steps', models.JSONField(default=list)),
                ('step_data', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='workflow_state', to='campaigns.campaign')),
            ],
            options={
                'db_table': 'campaign_workflow_state',
            },
        ),
    ]
