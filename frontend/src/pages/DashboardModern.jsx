import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  EyeIcon,
  StarIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  ChevronDownIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { dashboardService } from '../services/dashboardService';
import { SimpleBarChart, SimpleLineChart, RatingDistributionChart } from '../components/charts/SimpleChart';
import { SkeletonDashboard } from '../components/ui/Skeleton';

const DashboardModern = () => {
  const { user } = useAuth();

  // Helper functions
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index} className={index < rating ? 'text-yellow-400' : 'text-warmGray-300'}>
        ★
      </span>
    ));
  };
  const [selectedFilter, setSelectedFilter] = useState('CoffeeMeet Platform');
  const [selectedPeriod, setSelectedPeriod] = useState('Month');
  const [isLoading, setIsLoading] = useState(true);

  // Dynamic data states
  const [dashboardStats, setDashboardStats] = useState(null);
  const [recentEvaluations, setRecentEvaluations] = useState([]);
  const [ratingDistribution, setRatingDistribution] = useState([]);
  const [evaluationTrends, setEvaluationTrends] = useState([]);
  const [error, setError] = useState(null);

  // Données simulées pour le graphique - à remplacer par des données dynamiques
  const [dashboardData] = useState({
    totalCampaigns: 156,
    totalEmployees: 2847,
    totalReviews: 1203,
    chartData: [
      { day: 'M', value: 45 },
      { day: 'T', value: 52 },
      { day: 'W', value: 38 },
      { day: 'T', value: 61 },
      { day: 'F', value: 72 },
      { day: 'S', value: 58 },
      { day: 'S', value: 67 }
    ],
    recentReviews: [
      {
        id: 1,
        userName: 'Marie Dubois',
        avatar: 'MD',
        comment: 'Excellent coffee meeting experience! Really helped us connect with colleagues.',
        rating: 4.8,
        campaignName: 'Team Building Q4',
        timeAgo: '2 hours ago'
      },
      {
        id: 2,
        userName: 'Ahmed Hassan',
        avatar: 'AH',
        comment: 'Great platform for organizing coffee meetings. Very intuitive interface.',
        rating: 4.5,
        campaignName: 'New Employee Integration',
        timeAgo: '5 hours ago'
      },
      {
        id: 3,
        userName: 'Sophie Laurent',
        avatar: 'SL',
        comment: 'The matching algorithm works perfectly. Made meaningful connections.',
        rating: 5.0,
        campaignName: 'Cross-Department Mixer',
        timeAgo: '1 day ago'
      }
    ]
  });

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load all dashboard data in parallel
        const [
          statsResponse,
          evaluationsResponse,
          ratingsResponse,
          trendsResponse
        ] = await Promise.all([
          dashboardService.getOverallEvaluationStats(),
          dashboardService.getRecentEvaluations(4),
          dashboardService.getRatingDistribution(),
          dashboardService.getEvaluationTrends()
        ]);

        if (statsResponse.success) {
          setDashboardStats(statsResponse.data);
        }

        if (evaluationsResponse.success) {
          setRecentEvaluations(evaluationsResponse.data);
        }

        if (ratingsResponse.success) {
          setRatingDistribution(ratingsResponse.data);
        }

        if (trendsResponse.success) {
          setEvaluationTrends(trendsResponse.data);
        }

      } catch (err) {
        console.error('Dashboard loading error:', err);
        setError('Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [selectedFilter, selectedPeriod]);

  if (isLoading) {
    return <SkeletonDashboard />;
  }

  return (
    <div className="min-h-screen bg-cream p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        
        {/* En-tête */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-warmGray-800">Dashboard</h1>
            <p className="text-warmGray-600 mt-1">
              Welcome back, {user?.name || 'User'}! Here's your platform overview.
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <select 
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="bg-white/80 backdrop-blur-sm border border-warmGray-200 rounded-lg px-4 py-2 text-warmGray-700 focus:outline-none focus:ring-2 focus:ring-peach-400"
            >
              <option value="CoffeeMeet Platform">CoffeeMeet Platform</option>
              <option value="HR Department">HR Department</option>
              <option value="All Departments">All Departments</option>
            </select>
            
            <select 
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="bg-white/80 backdrop-blur-sm border border-warmGray-200 rounded-lg px-4 py-2 text-warmGray-700 focus:outline-none focus:ring-2 focus:ring-peach-400"
            >
              <option value="Week">Week</option>
              <option value="Month">Month</option>
              <option value="Quarter">Quarter</option>
              <option value="Year">Year</option>
            </select>
          </div>
        </div>

        {/* Statistiques principales */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Total Campaigns */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-warmGray-100/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-peach-100 rounded-xl flex items-center justify-center">
                  <ClipboardDocumentListIcon className="w-6 h-6 text-peach-600" />
                </div>
                <span className="text-sm font-medium text-warmGray-600 uppercase tracking-wide">
                  Total Campaigns
                </span>
              </div>
            </div>
            <div className="text-4xl font-bold text-warmGray-800 mb-2">
              {dashboardStats?.total_campaigns?.toLocaleString() || dashboardData.totalCampaigns.toLocaleString()}
            </div>
            <div className="flex items-center text-sm text-green-600">
              <span className="font-medium">+12%</span>
              <span className="text-warmGray-500 ml-1">vs last month</span>
            </div>
          </div>

          {/* Total Employees */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-warmGray-100/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                  <UserGroupIcon className="w-6 h-6 text-amber-600" />
                </div>
                <span className="text-sm font-medium text-warmGray-600 uppercase tracking-wide">
                  Total Employees
                </span>
              </div>
            </div>
            <div className="text-4xl font-bold text-warmGray-800 mb-2">
              {dashboardData.totalEmployees.toLocaleString()}
            </div>
            <div className="flex items-center text-sm text-green-600">
              <span className="font-medium">+8%</span>
              <span className="text-warmGray-500 ml-1">vs last month</span>
            </div>
          </div>

          {/* Total Reviews */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-warmGray-100/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                  <ChatBubbleLeftRightIcon className="w-6 h-6 text-orange-600" />
                </div>
                <span className="text-sm font-medium text-warmGray-600 uppercase tracking-wide">
                  Total Reviews
                </span>
              </div>
            </div>
            <div className="text-4xl font-bold text-warmGray-800 mb-2">
              {dashboardData.totalReviews.toLocaleString()}
            </div>
            <div className="flex items-center text-sm text-green-600">
              <span className="font-medium">+23%</span>
              <span className="text-warmGray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>

        {/* Section principale avec graphique et aide */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Graphique des statistiques */}
          <div className="lg:col-span-2 bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-warmGray-100/50">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-warmGray-800">My Stats</h2>
              <div className="flex items-center space-x-4">
                <select className="bg-transparent border border-warmGray-200 rounded-lg px-3 py-1 text-sm text-warmGray-600">
                  <option>CoffeeMeet Platform</option>
                </select>
                <select className="bg-transparent border border-warmGray-200 rounded-lg px-3 py-1 text-sm text-warmGray-600">
                  <option>Week</option>
                </select>
              </div>
            </div>

            {/* Graphique simplifié */}
            <div className="relative h-64 bg-gradient-to-br from-peach-50 to-amber-50 rounded-xl p-6 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-peach-100/30 to-amber-100/30"></div>
              
              {/* Labels de l'axe Y */}
              <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-warmGray-500 py-4">
                <span>120k</span>
                <span>96k</span>
                <span>48k</span>
                <span>0</span>
              </div>

              {/* Zone du graphique */}
              <div className="relative ml-8 h-full">
                <svg className="w-full h-full" viewBox="0 0 400 200">
                  {/* Ligne de graphique principale */}
                  <path
                    d="M 0 120 Q 60 100 120 140 Q 180 160 240 120 Q 300 100 360 110"
                    stroke="url(#gradient)"
                    strokeWidth="3"
                    fill="none"
                  />
                  
                  {/* Zone sous la courbe */}
                  <path
                    d="M 0 120 Q 60 100 120 140 Q 180 160 240 120 Q 300 100 360 110 L 360 200 L 0 200 Z"
                    fill="url(#areaGradient)"
                  />
                  
                  {/* Point highlight */}
                  <circle cx="240" cy="120" r="6" fill="#E8C4A0" stroke="#fff" strokeWidth="2" />
                  
                  {/* Tooltip simulé */}
                  <g transform="translate(200, 80)">
                    <rect x="0" y="0" width="100" height="40" rx="8" fill="#1f2937" opacity="0.9" />
                    <text x="50" y="16" textAnchor="middle" fill="white" fontSize="12" fontWeight="600">
                      48,752 View
                    </text>
                    <text x="50" y="32" textAnchor="middle" fill="#9ca3af" fontSize="10">
                      18 November 2024
                    </text>
                  </g>

                  <defs>
                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" style={{stopColor: '#E8C4A0', stopOpacity: 1}} />
                      <stop offset="100%" style={{stopColor: '#DDB892', stopOpacity: 1}} />
                    </linearGradient>
                    <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style={{stopColor: '#E8C4A0', stopOpacity: 0.3}} />
                      <stop offset="100%" style={{stopColor: '#E8C4A0', stopOpacity: 0.05}} />
                    </linearGradient>
                  </defs>
                </svg>
              </div>

              {/* Labels de l'axe X */}
              <div className="absolute bottom-0 left-8 right-0 flex justify-between text-xs text-warmGray-500 py-2">
                {dashboardData.chartData.map((point, index) => (
                  <span key={index}>{point.day}</span>
                ))}
              </div>
            </div>
          </div>

          {/* Section d'aide */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-warmGray-100/50">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-warmGray-800 mb-2">
                Do you need help?
              </h3>
              
              {/* Illustration d'aide */}
              <div className="my-6">
                <div className="relative w-32 h-32 mx-auto">
                  <div className="absolute inset-0 bg-gradient-to-br from-peach-100 to-amber-100 rounded-full"></div>
                  <div className="absolute inset-2 bg-white rounded-full flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-8 h-8 bg-peach-400 rounded-full mx-auto mb-1"></div>
                      <div className="w-12 h-1 bg-peach-200 rounded mx-auto mb-2"></div>
                      <div className="w-8 h-8 bg-peach-300 rounded mx-auto"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <button className="w-full bg-gradient-to-r from-[#E8C4A0] to-[#DDB892] hover:from-[#DDB892] hover:to-[#D4A574] text-[#8B6F47] font-medium py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] shadow-sm">
                Contact Support
              </button>
            </div>
          </div>
        </div>

        {/* Section des avis récents */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-warmGray-100/50">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-warmGray-800">Recent Reviews</h2>
            <button className="text-peach-600 hover:text-peach-700 font-medium text-sm transition-colors duration-200 flex items-center space-x-1">
              <span>See More</span>
              <ChevronDownIcon className="w-4 h-4 rotate-270" />
            </button>
          </div>

          <div className="space-y-4">
            {dashboardData.recentReviews.map((review) => (
              <div key={review.id} className="flex items-start space-x-4 p-4 bg-warmGray-50/70 backdrop-blur-sm rounded-xl border border-warmGray-100/50 hover:bg-warmGray-50/90 transition-all duration-200">
                
                {/* Avatar */}
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-peach-400 to-amber-400 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">{review.avatar}</span>
                  </div>
                </div>

                {/* Contenu de l'avis */}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-warmGray-800">
                        {review.userName} commented on "{review.campaignName}"
                      </h4>
                      <div className="flex items-center space-x-2 mt-1">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon
                              key={i}
                              className={`w-4 h-4 ${
                                i < Math.floor(review.rating)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-warmGray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm font-medium text-warmGray-700">
                          {review.rating}
                        </span>
                        <span className="text-sm text-warmGray-500">
                          • {review.timeAgo}
                        </span>
                      </div>
                    </div>
                    
                    <button className="text-peach-600 hover:text-peach-700 font-medium text-sm px-4 py-2 border border-peach-200 rounded-lg hover:bg-peach-50 transition-all duration-200">
                      Details
                    </button>
                  </div>
                  
                  <p className="text-warmGray-600 text-sm leading-relaxed">
                    {review.comment}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardModern;
