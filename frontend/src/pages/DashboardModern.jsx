import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  EyeIcon,
  StarIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  ChevronDownIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { dashboardService } from '../services/dashboardService';
import { SimpleBarChart, SimpleLineChart, RatingDistributionChart } from '../components/charts/SimpleChart';
import { SkeletonDashboard } from '../components/ui/Skeleton';

const DashboardModern = () => {
  const { user } = useAuth();

  // Helper functions
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index} className={index < rating ? 'text-yellow-400' : 'text-warmGray-300'}>
        ★
      </span>
    ));
  };
  const [selectedFilter, setSelectedFilter] = useState('CoffeeMeet Platform');
  const [selectedPeriod, setSelectedPeriod] = useState('Month');
  const [isLoading, setIsLoading] = useState(true);

  // Dynamic data states
  const [dashboardStats, setDashboardStats] = useState(null);
  const [recentEvaluations, setRecentEvaluations] = useState([]);
  const [ratingDistribution, setRatingDistribution] = useState([]);
  const [evaluationTrends, setEvaluationTrends] = useState([]);
  const [error, setError] = useState(null);

  // Données simulées pour le graphique - à remplacer par des données dynamiques
  const [dashboardData] = useState({
    totalCampaigns: 156,
    totalEmployees: 2847,
    totalReviews: 1203,
    chartData: [
      { day: 'M', value: 45 },
      { day: 'T', value: 52 },
      { day: 'W', value: 38 },
      { day: 'T', value: 61 },
      { day: 'F', value: 72 },
      { day: 'S', value: 58 },
      { day: 'S', value: 67 }
    ],
    recentReviews: [
      {
        id: 1,
        userName: 'Marie Dubois',
        avatar: 'MD',
        comment: 'Excellent coffee meeting experience! Really helped us connect with colleagues.',
        rating: 4.8,
        campaignName: 'Team Building Q4',
        timeAgo: '2 hours ago'
      },
      {
        id: 2,
        userName: 'Ahmed Hassan',
        avatar: 'AH',
        comment: 'Great platform for organizing coffee meetings. Very intuitive interface.',
        rating: 4.5,
        campaignName: 'New Employee Integration',
        timeAgo: '5 hours ago'
      },
      {
        id: 3,
        userName: 'Sophie Laurent',
        avatar: 'SL',
        comment: 'The matching algorithm works perfectly. Made meaningful connections.',
        rating: 5.0,
        campaignName: 'Cross-Department Mixer',
        timeAgo: '1 day ago'
      }
    ]
  });

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load all dashboard data in parallel
        const [
          statsResponse,
          evaluationsResponse,
          ratingsResponse,
          trendsResponse
        ] = await Promise.all([
          dashboardService.getOverallEvaluationStats(),
          dashboardService.getRecentEvaluations(4),
          dashboardService.getRatingDistribution(),
          dashboardService.getEvaluationTrends()
        ]);

        if (statsResponse.success) {
          setDashboardStats(statsResponse.data);
        }

        if (evaluationsResponse.success) {
          setRecentEvaluations(evaluationsResponse.data);
        }

        if (ratingsResponse.success) {
          setRatingDistribution(ratingsResponse.data);
        }

        if (trendsResponse.success) {
          setEvaluationTrends(trendsResponse.data);
        }

      } catch (err) {
        console.error('Dashboard loading error:', err);
        setError('Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [selectedFilter, selectedPeriod]);

  if (isLoading) {
    return <SkeletonDashboard />;
  }

  return (
    <div className="min-h-screen bg-cream p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        
        {/* En-tête */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-warmGray-800">Dashboard</h1>
            <p className="text-warmGray-600 mt-1">
              Welcome back, {user?.name || 'User'}! Here's your platform overview.
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <select 
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="bg-white/80 backdrop-blur-sm border border-warmGray-200 rounded-lg px-4 py-2 text-warmGray-700 focus:outline-none focus:ring-2 focus:ring-peach-400"
            >
              <option value="CoffeeMeet Platform">CoffeeMeet Platform</option>
              <option value="HR Department">HR Department</option>
              <option value="All Departments">All Departments</option>
            </select>
            
            <select 
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="bg-white/80 backdrop-blur-sm border border-warmGray-200 rounded-lg px-4 py-2 text-warmGray-700 focus:outline-none focus:ring-2 focus:ring-peach-400"
            >
              <option value="Week">Week</option>
              <option value="Month">Month</option>
              <option value="Quarter">Quarter</option>
              <option value="Year">Year</option>
            </select>
          </div>
        </div>

        {/* Statistiques principales */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Total Campaigns */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-warmGray-100/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-peach-100 rounded-xl flex items-center justify-center">
                  <ClipboardDocumentListIcon className="w-6 h-6 text-peach-600" />
                </div>
                <span className="text-sm font-medium text-warmGray-600 uppercase tracking-wide">
                  Total Campaigns
                </span>
              </div>
            </div>
            <div className="text-4xl font-bold text-warmGray-800 mb-2">
              {dashboardStats?.total_campaigns?.toLocaleString() || dashboardData.totalCampaigns.toLocaleString()}
            </div>
            <div className="flex items-center text-sm text-green-600">
              <span className="font-medium">+12%</span>
              <span className="text-warmGray-500 ml-1">vs last month</span>
            </div>
          </div>

          {/* Total Employees */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-warmGray-100/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                  <UserGroupIcon className="w-6 h-6 text-amber-600" />
                </div>
                <span className="text-sm font-medium text-warmGray-600 uppercase tracking-wide">
                  Total Employees
                </span>
              </div>
            </div>
            <div className="text-4xl font-bold text-warmGray-800 mb-2">
              {dashboardStats?.total_employees?.toLocaleString() || dashboardData.totalEmployees.toLocaleString()}
            </div>
            <div className="flex items-center text-sm text-green-600">
              <span className="font-medium">+8%</span>
              <span className="text-warmGray-500 ml-1">vs last month</span>
            </div>
          </div>

          {/* Total Reviews */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-warmGray-100/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                  <ChatBubbleLeftRightIcon className="w-6 h-6 text-orange-600" />
                </div>
                <span className="text-sm font-medium text-warmGray-600 uppercase tracking-wide">
                  Total Evaluations
                </span>
              </div>
            </div>
            <div className="text-4xl font-bold text-warmGray-800 mb-2">
              {dashboardStats?.total_evaluations?.toLocaleString() || dashboardData.totalReviews.toLocaleString()}
            </div>
            <div className="flex items-center text-sm text-green-600">
              <span className="font-medium">+23%</span>
              <span className="text-warmGray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>

        {/* Section principale avec graphique et aide */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Graphiques des évaluations */}
          <div className="lg:col-span-2 space-y-6">
            {/* Tendances des évaluations */}
            <SimpleLineChart
              data={evaluationTrends}
              title="Evaluation Trends Over Time"
            />

            {/* Distribution des notes */}
            <RatingDistributionChart
              data={ratingDistribution}
              title="Rating Distribution"
            />
          </div>

          {/* Recent Evaluations Sidebar */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-warmGray-100/50">
            <h2 className="text-xl font-semibold text-warmGray-800 mb-6">Recent Evaluations</h2>

            {recentEvaluations && recentEvaluations.length > 0 ? (
              <div className="space-y-4">
                {recentEvaluations.map((evaluation, index) => (
                  <div key={index} className="bg-warmGray-50/70 rounded-lg p-4 hover:bg-warmGray-100/70 transition-colors duration-200">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="text-sm font-semibold text-warmGray-800">{evaluation.employee_name}</p>
                        <p className="text-xs text-warmGray-600">with {evaluation.partner_name}</p>
                      </div>
                      <div className="flex items-center space-x-1">
                        {renderStars(evaluation.rating)}
                      </div>
                    </div>
                    {evaluation.comment && (
                      <p className="text-xs text-warmGray-700 italic mb-2 line-clamp-2">
                        "{evaluation.comment.substring(0, 60)}..."
                      </p>
                    )}
                    <div className="text-xs text-warmGray-500">
                      {formatDate(evaluation.submitted_at)}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <ChatBubbleLeftRightIcon className="h-12 w-12 text-warmGray-400 mx-auto mb-4" />
                <p className="text-warmGray-600">No recent evaluations</p>
              </div>
            )}
          </div>
        </div>


      </div>
    </div>
  );
};

export default DashboardModern;
