import { authAPI } from './api';

export const dashboardService = {
  // Get comprehensive dashboard data
  getDashboardData: async () => {
    try {
      const campaignsResult = await authAPI.getCampaigns();

      if (campaignsResult.success) {
        return {
          success: true,
          data: {
            campaigns: campaignsResult.data
          }
        };
      } else {
        throw new Error('Failed to fetch dashboard data');
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch dashboard data'
      };
    }
  },

  // Get campaigns with statistics
  getCampaignsWithStats: async () => {
    try {
      const result = await authAPI.getCampaigns();
      
      if (result.success) {
        // Enhance campaigns with additional statistics
        const campaignsWithStats = result.data.map((campaign) => {
          // For now, return campaigns without additional stats since API methods may not exist
          return {
            ...campaign,
            employees_count: 0,
            statistics: null
          };
        });

        return {
          success: true,
          data: campaignsWithStats
        };
      } else {
        throw new Error(result.error?.message || 'Failed to fetch campaigns');
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch campaigns with statistics'
      };
    }
  },

  // Get evaluation statistics across all campaigns
  getOverallEvaluationStats: async () => {
    try {
      const campaignsResult = await authAPI.getCampaigns();

      if (!campaignsResult.success) {
        // Return mock data if campaigns can't be fetched
        return {
          success: true,
          data: {
            total_campaigns: 0,
            total_evaluations: 0,
            total_pairs: 0,
            overall_average_rating: 0,
            overall_response_rate: 0,
            active_campaigns: 0,
            upcoming_campaigns: 0,
            completed_campaigns: 0
          }
        };
      }

      const campaigns = campaignsResult.data || [];

      // Calculate basic stats from campaigns data
      const now = new Date();
      const activeCampaigns = campaigns.filter(c => {
        if (!c.start_date || !c.end_date) return false;
        const startDate = new Date(c.start_date);
        const endDate = new Date(c.end_date);
        return startDate <= now && now <= endDate;
      }).length;

      const upcomingCampaigns = campaigns.filter(c => {
        if (!c.start_date) return false;
        const startDate = new Date(c.start_date);
        return startDate > now;
      }).length;

      const completedCampaigns = campaigns.filter(c => {
        if (!c.end_date) return false;
        const endDate = new Date(c.end_date);
        return endDate < now;
      }).length;

      return {
        success: true,
        data: {
          total_campaigns: campaigns.length,
          total_evaluations: campaigns.length * 5, // Mock data
          total_pairs: campaigns.length * 10, // Mock data
          overall_average_rating: 4.2, // Mock data
          overall_response_rate: 78.5, // Mock data
          active_campaigns: activeCampaigns,
          upcoming_campaigns: upcomingCampaigns,
          completed_campaigns: completedCampaigns
        }
      };
    } catch (error) {
      return {
        success: true, // Return success with empty data instead of failing
        data: {
          total_campaigns: 0,
          total_evaluations: 0,
          total_pairs: 0,
          overall_average_rating: 0,
          overall_response_rate: 0,
          active_campaigns: 0,
          upcoming_campaigns: 0,
          completed_campaigns: 0
        }
      };
    }
  },

  // Get recent activity data
  getRecentActivity: async () => {
    try {
      const campaignsResult = await authAPI.getCampaigns();

      if (!campaignsResult.success) {
        // Return empty activity if campaigns can't be fetched
        return {
          success: true,
          data: []
        };
      }

      const campaigns = campaignsResult.data || [];
      const recentActivity = [];

      // Get recent campaigns (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      campaigns.forEach(campaign => {
        if (campaign.created_at) {
          const createdDate = new Date(campaign.created_at);
          if (createdDate >= thirtyDaysAgo) {
            recentActivity.push({
              type: 'campaign_created',
              title: `Campaign "${campaign.title || campaign.name || 'Untitled'}" created`,
              date: campaign.created_at,
              campaign_id: campaign.id
            });
          }
        }

        // Check if campaign started recently
        if (campaign.start_date) {
          const startDate = new Date(campaign.start_date);
          if (startDate >= thirtyDaysAgo && startDate <= new Date()) {
            recentActivity.push({
              type: 'campaign_started',
              title: `Campaign "${campaign.title || campaign.name || 'Untitled'}" started`,
              date: campaign.start_date,
              campaign_id: campaign.id
            });
          }
        }
      });

      // Sort by date (most recent first)
      recentActivity.sort((a, b) => new Date(b.date) - new Date(a.date));

      return {
        success: true,
        data: recentActivity.slice(0, 10) // Return last 10 activities
      };
    } catch (error) {
      return {
        success: true, // Return success with empty data instead of failing
        data: []
      };
    }
  },

  // Get recent evaluations for dashboard
  getRecentEvaluations: async (limit = 4) => {
    try {
      // Mock data for now - replace with real API call when backend is ready
      const mockEvaluations = [
        {
          id: 1,
          employee_name: "Sarah Johnson",
          partner_name: "Mike Chen",
          rating: 5,
          comment: "Great conversation about project management techniques. Very insightful!",
          submitted_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          campaign_title: "Q4 Coffee Connections"
        },
        {
          id: 2,
          employee_name: "David Brown",
          partner_name: "Lisa Wang",
          rating: 4,
          comment: "Enjoyed discussing cross-team collaboration strategies.",
          submitted_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          campaign_title: "Innovation Meetups"
        },
        {
          id: 3,
          employee_name: "Emma Wilson",
          partner_name: "Alex Rodriguez",
          rating: 5,
          comment: "Fantastic meeting! Learned a lot about the marketing team's processes.",
          submitted_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          campaign_title: "Cross-Department Exchange"
        },
        {
          id: 4,
          employee_name: "James Miller",
          partner_name: "Sophie Taylor",
          rating: 3,
          comment: "Good conversation, though we could have gone deeper into technical topics.",
          submitted_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          campaign_title: "Tech Talk Sessions"
        }
      ];

      return {
        success: true,
        data: mockEvaluations.slice(0, limit)
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch recent evaluations'
      };
    }
  },

  // Get rating distribution data
  getRatingDistribution: async () => {
    try {
      // Mock data for now - replace with real API call when backend is ready
      const mockDistribution = [
        { rating: 1, count: 2 },
        { rating: 2, count: 5 },
        { rating: 3, count: 12 },
        { rating: 4, count: 28 },
        { rating: 5, count: 35 }
      ];

      return {
        success: true,
        data: mockDistribution
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch rating distribution'
      };
    }
  },

  // Get evaluation trends over time
  getEvaluationTrends: async (period = '6months') => {
    try {
      // Mock data for now - replace with real API call when backend is ready
      const mockTrends = [
        { label: 'Jan', value: 15 },
        { label: 'Feb', value: 22 },
        { label: 'Mar', value: 18 },
        { label: 'Apr', value: 31 },
        { label: 'May', value: 28 },
        { label: 'Jun', value: 35 }
      ];

      return {
        success: true,
        data: mockTrends
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to fetch evaluation trends'
      };
    }
  }
};
