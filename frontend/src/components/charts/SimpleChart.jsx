import React from 'react';

// Simple Bar Chart Component (without external libraries)
const SimpleBarChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-warmGray-800 mb-6">{title}</h3>
      <div className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="w-16 text-sm text-warmGray-600 font-medium">
              {item.label}
            </div>
            <div className="flex-1 bg-warmGray-200 rounded-full h-6 relative overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-[#E8C4A0] to-[#DDB892] rounded-full transition-all duration-700 ease-out"
                style={{ width: `${(item.value / maxValue) * 100}%` }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium text-warmGray-700">
                  {item.value}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Simple Line Chart Component
const SimpleLineChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue || 1;

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-warmGray-800 mb-6">{title}</h3>
      <div className="relative h-48">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-warmGray-500">
          <span>{maxValue}</span>
          <span>{Math.round((maxValue + minValue) / 2)}</span>
          <span>{minValue}</span>
        </div>
        
        {/* Chart area */}
        <div className="ml-8 h-full relative">
          <svg className="w-full h-full" viewBox="0 0 400 200">
            {/* Grid lines */}
            {[0, 1, 2, 3, 4].map(i => (
              <line
                key={i}
                x1="0"
                y1={i * 40}
                x2="400"
                y2={i * 40}
                stroke="#f3f4f6"
                strokeWidth="1"
              />
            ))}
            
            {/* Data line */}
            <polyline
              fill="none"
              stroke="#E8C4A0"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
              points={data.map((item, index) => {
                const x = (index / (data.length - 1)) * 400;
                const y = 200 - ((item.value - minValue) / range) * 200;
                return `${x},${y}`;
              }).join(' ')}
            />
            
            {/* Data points */}
            {data.map((item, index) => {
              const x = (index / (data.length - 1)) * 400;
              const y = 200 - ((item.value - minValue) / range) * 200;
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r="4"
                  fill="#DDB892"
                  stroke="#E8C4A0"
                  strokeWidth="2"
                />
              );
            })}
          </svg>
        </div>
        
        {/* X-axis labels */}
        <div className="ml-8 mt-2 flex justify-between text-xs text-warmGray-500">
          {data.map((item, index) => (
            <span key={index}>{item.label}</span>
          ))}
        </div>
      </div>
    </div>
  );
};

// Rating Distribution Chart
const RatingDistributionChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No ratings data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.count));
  const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#16a34a']; // Red to Green

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-warmGray-800 mb-6">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="flex items-center space-x-1 w-16">
              <span className="text-sm font-medium text-warmGray-700">{item.rating}</span>
              <span className="text-yellow-400">★</span>
            </div>
            <div className="flex-1 bg-warmGray-200 rounded-full h-6 relative overflow-hidden">
              <div
                className="h-full rounded-full transition-all duration-700 ease-out"
                style={{ 
                  width: `${(item.count / maxValue) * 100}%`,
                  backgroundColor: colors[index] || '#E8C4A0'
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium text-white">
                  {item.count}
                </span>
              </div>
            </div>
            <div className="w-12 text-xs text-warmGray-500">
              {maxValue > 0 ? Math.round((item.count / maxValue) * 100) : 0}%
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export { SimpleBarChart, SimpleLineChart, RatingDistributionChart };
