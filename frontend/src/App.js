import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import QueryProvider from './contexts/QueryProvider';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Dashboard from './pages/DashboardModern';
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import Campaigns from './pages/Campaigns';
import CampaignWorkflow from './pages/CampaignWorkflow';
import CampaignHistory from './pages/CampaignHistory';
import CampaignEvaluations from './pages/CampaignEvaluations';
import CampaignEvaluationsView from './pages/CampaignEvaluationsView';
import PublicEvaluation from './components/evaluation/PublicEvaluation';
import Employees from './pages/Employees';
import Settings from './pages/Settings';
import LandingPage from './pages/LandingPage';



function App() {
  return (
    <QueryProvider>
      <AuthProvider>
        <Router>
          <Routes>
            {/* Public evaluation route - no authentication required */}
            <Route path="/evaluation/:token" element={<PublicEvaluation />} />

            {/* Landing page as default route */}
            <Route path="/" element={<LandingPage />} />

            {/* Public auth routes - without layout */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />

            {/* Protected routes - with layout and protection */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/campaigns/*" element={
              <ProtectedRoute>
                <Layout>
                  <Campaigns />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/campaigns/:id/workflow" element={
              <ProtectedRoute>
                <Layout>
                  <CampaignWorkflow />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/campaigns/:id/history" element={
              <ProtectedRoute>
                <Layout>
                  <CampaignHistory />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/campaigns/:id/evaluations" element={
              <ProtectedRoute>
                <Layout>
                  <CampaignEvaluations />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/campaigns/:id/feedback" element={
              <ProtectedRoute>
                <Layout>
                  <CampaignEvaluationsView />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/employees/*" element={
              <ProtectedRoute>
                <Layout>
                  <Employees />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/settings" element={
              <ProtectedRoute>
                <Layout>
                  <Settings />
                </Layout>
              </ProtectedRoute>
            } />

            {/* Catch all route - redirect to login */}
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </QueryProvider>
  );
}

export default App;
